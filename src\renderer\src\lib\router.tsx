import { createHashRouter, Navigate } from 'react-router-dom'
import Layout from '../components/shared/Layout'
import ComponentShowcase from '../components/ComponentShowcase'
import { AppWithShortcuts } from '../components/layout/AppWithShortcuts'

// Page components
import DashboardPage from '../pages/DashboardPage'
import InboxPage from '../pages/InboxPage'
import ProjectsPage from '../pages/ProjectsPage'
import AreasPage from '../pages/AreasPage'
import ResourcesPage from '../pages/ResourcesPage'
import ArchivePage from '../pages/ArchivePage'
import ReviewsPage from '../pages/ReviewsPage'
import SettingsPage from '../pages/SettingsPage'
import InspirationCapturePage from '../pages/InspirationCapturePage'

// Feature components for detail pages
import ProjectDetailPage from '../components/features/ProjectDetailPage'
import AreaDetailPage from '../components/features/AreaDetailPage'

// Route configuration
export const router = createHashRouter([
  {
    path: '/',
    element: <AppWithShortcuts />,
    children: [
      {
        path: '',
        element: <Layout />,
        children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
        handle: {
          crumbKey: 'nav.dashboard'
        }
      },
      {
        path: 'inbox',
        element: <InboxPage />,
        handle: {
          crumbKey: 'nav.inbox'
        }
      },

      {
        path: 'projects',
        element: <ProjectsPage />,
        handle: {
          crumbKey: 'nav.projects'
        },
        children: [
          {
            path: ':projectId',
            element: <ProjectDetailPage />,
            handle: {
              crumb: (data: any) => data?.project?.name || 'Project'
            }
          }
        ]
      },
      {
        path: 'areas',
        element: <AreasPage />,
        handle: {
          crumbKey: 'nav.areas'
        },
        children: [
          {
            path: ':areaId',
            element: <AreaDetailPage />,
            handle: {
              crumb: (data: any) => data?.area?.name || 'Area'
            }
          }
        ]
      },
      {
        path: 'resources',
        element: <ResourcesPage />,
        handle: {
          crumbKey: 'nav.resources'
        },
        children: [
          {
            path: ':resourceId',
            element: <div>Resource Detail</div>,
            handle: {
              crumb: (data: any) => data?.resource?.title || 'Resource'
            }
          }
        ]
      },
      {
        path: 'archive',
        element: <ArchivePage />,
        handle: {
          crumbKey: 'nav.archive'
        }
      },
      {
        path: 'reviews',
        element: <ReviewsPage />,
        handle: {
          crumbKey: 'nav.reviews'
        }
      },
      {
        path: 'settings',
        element: <SettingsPage />,
        handle: {
          crumbKey: 'nav.settings'
        }
      },
      {
        path: 'components',
        element: <ComponentShowcase />,
        handle: {
          crumb: () => 'Components'
        }
      }
        ]
      },
      {
        path: 'inspiration-capture',
        element: <InspirationCapturePage />,
        handle: {
          crumb: () => 'Inspiration Capture'
        }
      }
    ]
  }
])

// 导航路由定义 - 中文本地化
export const routes = [
  {
    path: '/dashboard',
    name: '仪表盘',
    icon: 'dashboard',
    category: 'main'
  },
  {
    path: '/inbox',
    name: '收件箱',
    icon: 'inbox',
    category: 'main'
  },
  {
    path: '/projects',
    name: '项目',
    icon: 'project',
    category: 'main'
  },
  {
    path: '/areas',
    name: '领域',
    icon: 'area',
    category: 'main'
  },
  {
    path: '/resources',
    name: '资源',
    icon: 'resource',
    category: 'main'
  },
  {
    path: '/archive',
    name: '归档',
    icon: 'archive',
    category: 'main'
  },
  {
    path: '/reviews',
    name: '回顾',
    icon: 'review',
    category: 'main'
  },
  {
    path: '/settings',
    name: '设置',
    icon: 'settings',
    category: 'main'
  }
] as const

export type RouteCategory = 'main' | 'para' | 'tools'
export type RouteName = (typeof routes)[number]['name']
export type RoutePath = (typeof routes)[number]['path']

export default router
