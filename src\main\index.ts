import { app, shell, BrowserWindow, ipcMain, protocol, globalShortcut } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../build/icon.png?asset'
import databaseService from './database'
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import ipcHandler from './ipc'

// Register schemes as privileged before app is ready
protocol.registerSchemesAsPrivileged([
  {
    scheme: 'vditor',
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  },
  {
    scheme: 'paolife-assets',
    privileges: {
      standard: true,
      secure: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  }
])

// Global reference to main window
let mainWindow: BrowserWindow | null = null
let inspirationWindow: BrowserWindow | null = null

// Global shortcuts registry
const registeredShortcuts = new Map<string, string>() // accelerator -> action

// Pre-create inspiration window for faster access
function preCreateInspirationWindow(shouldShow = false): void {
  if (inspirationWindow && !inspirationWindow.isDestroyed()) {
    if (shouldShow) {
      if (inspirationWindow.isMinimized()) {
        inspirationWindow.restore()
      }
      inspirationWindow.show()
      inspirationWindow.setOpacity(1)
      inspirationWindow.focus()
    }
    return
  }

  // Build platform-aware options for a translucent floating window
  const inspirationOptions: Electron.BrowserWindowConstructorOptions & Record<string, any> = {
    width: 600,
    height: 280,
    frame: false,
    show: false, // Don't show initially
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    transparent: true,
    hasShadow: false,
    backgroundColor: '#00000000', // Fully transparent
    opacity: 0, // Start with invisible
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      webSecurity: !is.dev,
      allowRunningInsecureContent: is.dev
    }
  }

  // macOS: enable vibrancy/visual effect
  if (process.platform === 'darwin') {
    inspirationOptions.vibrancy = 'under-window'
    inspirationOptions.visualEffectState = 'active'
  }

  // Windows 11+: enable acrylic/mica material when available
  if (process.platform === 'win32') {
    // backgroundMaterial is a Windows-only experimental option in Electron
    inspirationOptions.backgroundMaterial = 'acrylic'
  }

  inspirationWindow = new BrowserWindow(inspirationOptions)

  // Center the window on screen
  inspirationWindow.center()

  // Load the inspiration capture page
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    inspirationWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}#/inspiration-capture`)
  } else {
    inspirationWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'inspiration-capture'
    })
  }

  // Show window when ready (if requested)
  if (shouldShow) {
    inspirationWindow.once('ready-to-show', () => {
      inspirationWindow?.show()
      // Fade in the window
      inspirationWindow?.setOpacity(1)
      inspirationWindow?.focus()
    })
  }

  // Handle window closed
  inspirationWindow.on('closed', () => {
    inspirationWindow = null
  })

  // Hide window when it loses focus
  inspirationWindow.on('blur', () => {
    if (inspirationWindow) {
      inspirationWindow.setOpacity(0)
      setTimeout(() => {
        if (inspirationWindow && !inspirationWindow.isDestroyed()) {
          inspirationWindow.hide()
        }
      }, 100)
    }
  })
}

// Show inspiration capture window
function createInspirationWindow(): void {
  preCreateInspirationWindow(true) // Create and show if needed
}

// Global shortcut management functions
function handleGlobalShortcut(action: string) {
  console.log('Global shortcut triggered:', action)

  switch (action) {
    case 'inbox-quick-capture':
      // Create and show inspiration capture window
      createInspirationWindow()
      break
    case 'inbox-navigate':
      // Show main window and navigate to inbox
      if (mainWindow) {
        if (mainWindow.isMinimized()) {
          mainWindow.restore()
        }
        mainWindow.show()
        mainWindow.focus()
        mainWindow.webContents.send('global-navigate', '/inbox')
      }
      break
    default:
      console.log('Unknown global shortcut action:', action)
  }
}

export function registerGlobalShortcut(accelerator: string, action: string): boolean {
  try {
    // Unregister existing shortcut if any
    if (registeredShortcuts.has(accelerator)) {
      globalShortcut.unregister(accelerator)
    }

    // Register new shortcut
    const ret = globalShortcut.register(accelerator, () => {
      handleGlobalShortcut(action)
    })

    if (ret) {
      registeredShortcuts.set(accelerator, action)
      console.log(`Global shortcut registered: ${accelerator} -> ${action}`)
    } else {
      console.log(`Failed to register global shortcut: ${accelerator}`)
    }

    return ret
  } catch (error) {
    console.error('Error registering global shortcut:', error)
    return false
  }
}

export function unregisterGlobalShortcut(accelerator: string): void {
  try {
    globalShortcut.unregister(accelerator)
    registeredShortcuts.delete(accelerator)
    console.log(`Global shortcut unregistered: ${accelerator}`)
  } catch (error) {
    console.error('Error unregistering global shortcut:', error)
  }
}

export function unregisterAllGlobalShortcuts(): void {
  try {
    globalShortcut.unregisterAll()
    registeredShortcuts.clear()
    console.log('All global shortcuts unregistered')
  } catch (error) {
    console.error('Error unregistering all global shortcuts:', error)
  }
}

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    frame: false,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      // 开发模式下允许访问外部资源
      webSecurity: !is.dev,
      allowRunningInsecureContent: is.dev
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.maximize()
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    // For testing vditor protocol, load test page first
    if (process.env.TEST_VDITOR_PROTOCOL) {
      mainWindow.loadFile(join(__dirname, '../../test-vditor-protocol.html'))
    } else {
      mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Register protocol to serve vditor static files
  protocol.handle('vditor', async (request) => {
    const url = request.url.substring(9) // Remove 'vditor://' prefix
    // Remove leading 'dist/' if present since our resources are already in dist folder
    const cleanUrl = url.startsWith('dist/') ? url.substring(5) : url
    const resourcePath = join(__dirname, '../../resources/dist', cleanUrl)

    try {
      const { readFile } = await import('fs/promises')
      const data = await readFile(resourcePath)

      // Determine content type based on file extension
      let contentType = 'text/plain'
      if (url.endsWith('.css')) {
        contentType = 'text/css'
      } else if (url.endsWith('.js')) {
        contentType = 'application/javascript'
      } else if (url.endsWith('.json')) {
        contentType = 'application/json'
      } else if (url.endsWith('.png')) {
        contentType = 'image/png'
      } else if (url.endsWith('.svg')) {
        contentType = 'image/svg+xml'
      }

      return new Response(data, {
        headers: { 'Content-Type': contentType }
      })
    } catch (error) {
      console.error(
        'Failed to load vditor resource:',
        resourcePath,
        'Original URL:',
        request.url,
        error
      )
      return new Response('Not Found', { status: 404 })
    }
  })

  // Register protocol to serve local assets (images, etc.)
  protocol.handle('paolife-assets', async (request) => {
    const url = request.url.substring(17) // Remove 'paolife-assets://' prefix
    const userDataPath = app.getPath('userData')
    const resourcePath = join(userDataPath, url)

    try {
      const { readFile } = await import('fs/promises')
      const data = await readFile(resourcePath)

      // Determine content type based on file extension
      let contentType = 'application/octet-stream'
      if (url.endsWith('.png')) {
        contentType = 'image/png'
      } else if (url.endsWith('.jpg') || url.endsWith('.jpeg')) {
        contentType = 'image/jpeg'
      } else if (url.endsWith('.gif')) {
        contentType = 'image/gif'
      } else if (url.endsWith('.svg')) {
        contentType = 'image/svg+xml'
      } else if (url.endsWith('.webp')) {
        contentType = 'image/webp'
      }

      return new Response(data, {
        headers: { 'Content-Type': contentType }
      })
    } catch (error) {
      console.error('Failed to load asset:', resourcePath, 'Original URL:', request.url, error)
      return new Response('Not Found', { status: 404 })
    }
  })

  // Initialize database
  const dbResult = await databaseService.initialize()
  if (!dbResult.success) {
    console.error('Failed to initialize database:', dbResult.error)
    app.quit()
    return
  }

  // Initialize file system
  const fsResult = await fileSystemService.initialize()
  if (!fsResult.success) {
    console.error('Failed to initialize file system:', fsResult.error)
    app.quit()
    return
  }

  // Initialize file watcher
  const watcherResult = await fileWatcherService.initialize()
  if (!watcherResult.success) {
    console.error('Failed to initialize file watcher:', watcherResult.error)
    // File watcher failure is not critical, continue without it
  } else {
    // Set up file system event handlers
    fileWatcherService.on('file-system-event', (event) => {
      console.log('File system event:', event)
      // TODO: Broadcast to renderer processes via IPC
    })

    fileWatcherService.on('error', (error) => {
      console.error('File watcher error:', error)
    })
  }

  // Initialize IPC handlers
  ipcHandler.initialize()

  // Set up periodic cleanup tasks
  setInterval(() => {
    fileSystemService.cleanupLocks()
  }, 60000) // Clean up locks every minute

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)

    // Add shortcuts for DevTools in all environments
    window.webContents.on('before-input-event', (event, input) => {
      // F12 shortcut
      if (input.key === 'F12') {
        window.webContents.toggleDevTools()
      }
      // Ctrl+Shift+I shortcut
      if (input.control && input.shift && input.key === 'I') {
        window.webContents.toggleDevTools()
      }
    })
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  createWindow()

  // Register default global shortcuts
  const registerDefaultGlobalShortcuts = () => {
    // Note: Global shortcuts are now managed by the shortcuts store
    // Default shortcuts will be registered when the renderer initializes
    console.log('Global shortcuts will be managed by shortcuts store')
  }

  // Register default shortcuts after window is created
  // registerDefaultGlobalShortcuts() // Disabled: Now managed by shortcuts store

  // Pre-create inspiration window for faster access
  preCreateInspirationWindow(false)

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  // Unregister all global shortcuts
  globalShortcut.unregisterAll()

  // Clean up IPC handlers
  ipcHandler.cleanup()

  // Close file watcher
  await fileWatcherService.close()

  // Close database connection
  await databaseService.close()

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
