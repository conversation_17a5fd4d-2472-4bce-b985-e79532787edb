import { Outlet, useLocation, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Navigation from './Navigation'
import Breadcrumbs from './Breadcrumbs'
import { ErrorBoundary } from './ErrorBoundary'
import { ToastContainer } from '../ui/toast'
import { useUIStore } from '../../store/uiStore'


export function Layout() {
  const location = useLocation()
  const params = useParams()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const { notifications, removeNotification } = useUIStore()

  // {{ AURA-X: Add - 获取当前页面的项目或领域ID. Approval: 寸止(ID:1738157400). }}
  const projectId = params.projectId
  const areaId = params.areaId

  // Handle page transitions - disabled to prevent flashing
  // useEffect(() => {
  //   setIsTransitioning(true)
  //   const timer = setTimeout(() => setIsTransitioning(false), 150)
  //   return () => clearTimeout(timer)
  // }, [location.pathname])

  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar Navigation */}
      <aside className="w-64 sidebar-layout flex-shrink-0 scrollbar-hidden overflow-y-auto">
        <Navigation />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar with Breadcrumbs */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between px-6 py-3">
            <Breadcrumbs />

            {/* Window Controls (for Electron) */}
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => window.electronAPI?.window.close()}
                className="w-3.5 h-3.5 rounded-full bg-red-500 hover:bg-red-600 flex justify-center items-center group"
                title="关闭"
                aria-label="Close"
              >
                <svg className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <button
                type="button"
                onClick={() => window.electronAPI?.window.maximize()}
                className="w-3.5 h-3.5 rounded-full bg-green-400 hover:bg-green-500 flex justify-center items-center group"
                title="最大化"
                aria-label="Maximize"
              >
                <svg className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M3 3h18v18H3z" />
                </svg>
              </button>
              <button
                type="button"
                onClick={() => window.electronAPI?.window.minimize()}
                className="w-3.5 h-3.5 rounded-full bg-yellow-400 hover:bg-yellow-500 flex justify-center items-center group"
                title="最小化"
                aria-label="Minimize"
              >
                <svg className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M4 12h16" />
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto scrollbar-hidden">
            <ErrorBoundary>
              <div className="opacity-100 transform translate-y-0">
                <Outlet />
              </div>
            </ErrorBoundary>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onRemove={removeNotification} />


    </div>
  )
}

export default Layout
