<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaoLife Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        /* Custom scrollbar for a more modern look */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        ::-webkit-scrollbar-thumb {
            background: #475569;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }
        /* Simple animation for modal */
        .modal-enter {
            animation: fadeIn 0.3s ease-out;
        }
        .modal-leave {
            animation: fadeOut 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: scale(1); }
            to { opacity: 0; transform: scale(0.95); }
        }
        .gemini-spinner {
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-left-color: #a78bfa;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">

    <div class="flex h-screen">
        <!-- Sidebar Navigation -->
        <aside class="w-64 flex-shrink-0 bg-slate-950/70 border-r border-slate-800 flex flex-col">
            <div class="h-16 flex items-center px-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-indigo-400"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m9 12 2 2 4-4"/></svg>
                <h1 class="text-xl font-bold ml-2 text-white">PaoLife</h1>
            </div>
            <nav class="flex-1 px-4 py-4 space-y-2">
                <a href="#" class="flex items-center px-4 py-2 text-white bg-indigo-500/20 border border-indigo-500/30 rounded-lg">
                    <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                    仪表盘
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="inbox" class="w-5 h-5 mr-3"></i>
                    收件箱
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="kanban-square" class="w-5 h-5 mr-3"></i>
                    项目管理
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="target" class="w-5 h-5 mr-3"></i>
                    领域管理
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="library" class="w-5 h-5 mr-3"></i>
                    资源库
                </a>
                <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="archive" class="w-5 h-5 mr-3"></i>
                    归档管理
                </a>
                 <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="book-check" class="w-5 h-5 mr-3"></i>
                    复盘总结
                </a>
            </nav>
            <div class="px-4 py-4 border-t border-slate-800">
                 <a href="#" class="flex items-center px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200">
                    <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                    设置
                </a>
                <div class="flex items-center mt-4 px-4">
                    <img class="h-10 w-10 rounded-full" src="https://placehold.co/100x100/6366f1/e0e7ff?text=User" alt="User Avatar">
                    <div class="ml-3">
                        <p class="text-sm font-medium text-white">张伟</p>
                        <p class="text-xs text-slate-400">Pro Plan</p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <!-- Header & Quick Capture -->
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-white">下午好, 张伟! 👋</h1>
                        <p class="text-slate-400 mt-1" id="current-date">今天是 2025年8月15日, 星期五</p>
                    </div>
                    <div class="relative mt-4 md:mt-0 w-full md:w-96">
                        <i data-lucide="plus-circle" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 w-5 h-5"></i>
                        <input type="text" placeholder="快速捕捉你的想法..." class="w-full bg-slate-800 border border-slate-700 rounded-lg pl-10 pr-4 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 transition">
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    
                    <!-- Left Column -->
                    <div class="lg:col-span-2 space-y-6">
                        
                        <!-- Review Prompt -->
                        <div id="review-prompt" class="bg-indigo-900/50 border border-indigo-700/60 rounded-lg p-5 flex items-center justify-between shadow-lg">
                            <div class="flex items-center">
                                <div class="bg-indigo-500/20 p-3 rounded-full">
                                    <i data-lucide="calendar-check" class="w-6 h-6 text-indigo-300"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-white">是时候进行你的周复盘了!</h3>
                                    <p class="text-indigo-200 text-sm">让 AI 帮你总结过去一周，开启新的一周。</p>
                                </div>
                            </div>
                            <button id="gemini-review-btn" class="bg-indigo-600 text-white font-semibold px-4 py-2 rounded-lg hover:bg-indigo-500 transition-colors duration-200 whitespace-nowrap flex items-center">
                                ✨ AI 智能复盘
                            </button>
                        </div>
                        
                        <!-- Focus & Action -->
                        <div class="bg-slate-800/50 border border-slate-700/50 rounded-lg shadow-lg">
                            <div class="p-5 border-b border-slate-700">
                                <h2 class="text-xl font-bold text-white flex items-center">
                                    <i data-lucide="zap" class="w-6 h-6 mr-3 text-yellow-400"></i>
                                    行动焦点
                                </h2>
                                <div class="mt-4">
                                    <div class="border-b border-slate-700">
                                        <nav class="-mb-px flex space-x-6" id="action-tabs">
                                            <button data-tab="today" class="tab-btn active text-indigo-400 border-indigo-400 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm">今日待办</button>
                                            <button data-tab="upcoming" class="tab-btn text-slate-400 border-transparent whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm hover:text-white">即将到期</button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                            <div class="p-5 space-y-4" id="action-content">
                                <!-- Content will be dynamically inserted here -->
                            </div>
                        </div>

                        <!-- Project Radar -->
                        <div>
                            <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                                <i data-lucide="radar" class="w-6 h-6 mr-3 text-cyan-400"></i>
                                项目雷达
                            </h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="project-radar">
                                <!-- Project cards will be dynamically inserted here -->
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Areas & Habits -->
                        <div>
                            <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                                <i data-lucide="shield-check" class="w-6 h-6 mr-3 text-emerald-400"></i>
                                领域与习惯
                            </h2>
                            <div class="space-y-4" id="areas-habits">
                                <!-- Area cards will be dynamically inserted here -->
                            </div>
                        </div>

                        <!-- Inbox & Recently Active -->
                        <div>
                             <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                                <i data-lucide="history" class="w-6 h-6 mr-3 text-rose-400"></i>
                                快速访问
                            </h2>
                            <div class="bg-slate-800/50 border border-slate-700/50 rounded-lg shadow-lg p-5 space-y-4">
                                <!-- Inbox -->
                                <div class="flex justify-between items-center">
                                    <span class="text-slate-300 font-medium">收件箱</span>
                                    <a href="#" class="flex items-center text-sm font-semibold text-indigo-400 hover:text-indigo-300">
                                        处理 <span class="bg-indigo-500/20 text-indigo-300 text-xs font-bold ml-2 px-2 py-0.5 rounded-full">3</span>
                                        <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                                    </a>
                                </div>
                                <!-- Recently Active -->
                                <div>
                                    <h3 class="text-slate-300 font-medium mb-2">最近活跃</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li class="flex items-center">
                                            <i data-lucide="kanban-square" class="w-4 h-4 mr-2 text-cyan-400"></i>
                                            <a href="#" class="truncate hover:text-white">项目: PaoLife V1 开发</a>
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="target" class="w-4 h-4 mr-2 text-emerald-400"></i>
                                            <a href="#" class="truncate hover:text-white">领域: 个人成长</a>
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="file-text" class="w-4 h-4 mr-2 text-slate-400"></i>
                                            <a href="#" class="truncate hover:text-white">笔记: 关于PARA方法的思考.md</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Gemini AI Modal -->
    <div id="gemini-modal" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 hidden">
        <div id="gemini-modal-content" class="modal-enter bg-slate-800 border border-slate-700 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] flex flex-col">
            <div class="flex items-center justify-between p-4 border-b border-slate-700">
                <h3 id="gemini-modal-title" class="text-lg font-bold text-white flex items-center">
                    <i data-lucide="sparkles" class="w-5 h-5 mr-2 text-indigo-400"></i>
                    AI 助手
                </h3>
                <button id="gemini-modal-close" class="text-slate-400 hover:text-white">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="gemini-modal-body" class="p-6 overflow-y-auto">
                <!-- Loading state -->
                <div id="gemini-loading" class="flex flex-col items-center justify-center text-center text-slate-400 hidden">
                    <div class="gemini-spinner"></div>
                    <p class="mt-4">正在为您生成建议，请稍候...</p>
                </div>
                <!-- Result state -->
                <div id="gemini-result" class="prose prose-invert max-w-none text-slate-300">
                    <!-- AI generated content will be here -->
                </div>
            </div>
            <div class="p-4 bg-slate-800/50 border-t border-slate-700 flex justify-end space-x-3">
                <button id="gemini-modal-action-btn" class="bg-indigo-600 text-white font-semibold px-4 py-2 rounded-lg hover:bg-indigo-500 transition-colors duration-200 hidden">添加到任务</button>
                <button id="gemini-modal-cancel-btn" class="bg-slate-700 text-white font-semibold px-4 py-2 rounded-lg hover:bg-slate-600 transition-colors duration-200">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // --- MOCK DATA ---
        const MOCK_CURRENT_DATE = new Date('2025-08-15T09:00:00');
        const mockData = {
            tasks: [
                { id: 1, content: '完成仪表盘UI原型设计', completed: false, priority: 'high', deadline: new Date('2025-08-13T23:59:59'), projectId: 1, projectName: 'PaoLife V1 开发' },
                { id: 2, content: '修复登录页面的CSS Bug', completed: false, priority: 'high', deadline: new Date('2025-08-15T23:59:59'), projectId: 1, projectName: 'PaoLife V1 开发' },
                { id: 3, content: '撰写项目周报', completed: false, priority: 'medium', deadline: new Date('2025-08-15T23:59:59'), projectId: 1, projectName: 'PaoLife V1 开发' },
                { id: 4, content: '预约牙医年度检查', completed: true, priority: 'low', deadline: new Date('2025-08-15T23:59:59'), projectId: null, areaName: '健康' },
                { id: 5, content: '研究并购买新的投资理财产品', completed: false, priority: 'medium', deadline: new Date('2025-08-17T23:59:59'), projectId: 2, projectName: 'Q3 财务规划' },
                { id: 6, content: '准备下周市场会议的PPT', completed: false, priority: 'high', deadline: new Date('2025-08-18T23:59:59'), projectId: 1, projectName: 'PaoLife V1 开发' },
                { id: 7, content: '整理并归档第二季度发票', completed: false, priority: 'low', deadline: new Date('2025-08-22T23:59:59'), projectId: 2, projectName: 'Q3 财务规划' },
            ],
            projects: [
                { id: 1, name: 'PaoLife V1 开发', status: 'At Risk', progress: 65, deadline: new Date('2025-09-30T23:59:59'), areaName: '职业发展', tasksCompleted: 35, tasksTotal: 80, statusColor: 'yellow' },
                { id: 2, name: 'Q3 财务规划', status: 'On Track', progress: 40, deadline: new Date('2025-09-30T23:59:59'), areaName: '财务', tasksCompleted: 4, tasksTotal: 10, statusColor: 'green' },
                { id: 3, name: '欧洲夏季旅行计划', status: 'On Track', progress: 80, deadline: new Date('2025-08-25T23:59:59'), areaName: '生活', tasksCompleted: 16, tasksTotal: 20, statusColor: 'green' },
            ],
            areas: [
                { id: 1, name: '健康', standard: '保持每月至少运动10次，体重维持在75公斤以下。', habits: [{ name: '健身', records: [true, true, false, true, true, false, false] }, { name: '冥想', records: [true, true, true, false, true, true, true] }], metrics: [ { name: '体重', value: '74.8kg' }, { name: '本周运动', value: '3.5小时' }] },
                { id: 2, name: '个人成长', standard: '每月阅读2本书，完成1门在线课程。', habits: [{ name: '阅读30分钟', records: [true, true, true, true, true, false, false] }], metrics: [ { name: '本月已读', value: '1/2 本' }] }
            ]
        };

        // --- GEMINI API SIMULATION ---
        const callGeminiAPI = (prompt) => {
            console.log("--- Sending prompt to Gemini API (Simulation) ---");
            console.log(prompt);
            
            return new Promise(resolve => {
                setTimeout(() => {
                    let response = '';
                    if (prompt.includes('任务分解')) {
                        response = `
                            <h4>项目 "PaoLife V1 开发" 的任务建议：</h4>
                            <p>基于项目名称，以下是一些建议的任务分解，可以帮助您启动项目：</p>
                            <ul>
                                <li><strong>阶段一：规划与设计</strong>
                                    <ul>
                                        <li>确定核心功能与 MVP (最小可行产品) 范围</li>
                                        <li>创建产品线框图和 UI/UX 设计稿</li>
                                        <li>设计数据库 schema</li>
                                    </ul>
                                </li>
                                <li><strong>阶段二：开发与实现</strong>
                                    <ul>
                                        <li>搭建项目脚手架 (Electron + React + Vite)</li>
                                        <li>开发用户认证模块</li>
                                        <li>实现 P.A.R.A. 四大核心模块的 CRUD 功能</li>
                                        <li>开发仪表盘数据聚合与展示功能</li>
                                    </ul>
                                </li>
                                <li><strong>阶段三：测试与发布</strong>
                                    <ul>
                                        <li>进行单元测试和集成测试</li>
                                        <li>修复已知 Bug</li>
                                        <li>打包应用并准备发布</li>
                                    </ul>
                                </li>
                            </ul>
                        `;
                    } else if (prompt.includes('周复盘')) {
                        response = `
                            <h4>周复盘智能总结</h4>
                            <p>根据您过去一周的表现，AI 为您生成了以下总结，希望能为您提供一些启发：</p>
                            <h5>做得不错 👍</h5>
                            <ul>
                                <li><strong>习惯养成</strong>: 您在“冥想”和“阅读”方面表现出色，保持了很高的完成率，这对于维持精力和专注力非常有帮助。</li>
                                <li><strong>项目推进</strong>: “欧洲夏季旅行计划”项目进展顺利，已完成80%，离目标越来越近了！</li>
                            </ul>
                            <h5>值得关注 🤔</h5>
                            <ul>
                                <li><strong>项目风险</strong>: “PaoLife V1 开发”项目目前处于“有风险”状态。虽然进度已过半，但可能需要您投入更多关注，检查是否存在瓶颈或延期风险。</li>
                                <li><strong>习惯一致性</strong>: “健身”习惯的完成率稍有波动，可以思考一下是什么原因导致的，并为下周制定更具体可行的计划。</li>
                            </ul>
                            <h5>下周建议 🚀</h5>
                            <p><strong>聚焦核心项目</strong>: 建议下周优先处理“PaoLife V1 开发”项目中的高优先级任务，尝试将其状态扭转为“进行中”。</p>
                        `;
                    } else {
                        response = "<p>抱歉，我无法理解您的请求。</p>";
                    }
                    console.log("--- Received mock response from Gemini API ---");
                    resolve(response);
                }, 1500); // Simulate network delay
            });
        };

        // --- UTILITY & RENDER FUNCTIONS (Mostly unchanged) ---
        const formatDate = (date) => new Intl.DateTimeFormat('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }).format(date);
        const daysUntil = (targetDate) => Math.ceil((targetDate.getTime() - MOCK_CURRENT_DATE.getTime()) / (1000 * 60 * 60 * 24));

        const renderTasks = (filter) => {
            const container = document.getElementById('action-content');
            container.innerHTML = '';
            let tasksToRender = [];
            if (filter === 'today') {
                tasksToRender = mockData.tasks.filter(t => !t.completed && (daysUntil(t.deadline) <= 0 || daysUntil(t.deadline) === 1));
            } else {
                tasksToRender = mockData.tasks.filter(t => !t.completed && daysUntil(t.deadline) > 1 && daysUntil(t.deadline) <= 7);
            }
            if (tasksToRender.length === 0) {
                container.innerHTML = `<div class="text-center py-8 text-slate-400"><i data-lucide="party-popper" class="w-10 h-10 mx-auto mb-2"></i><p>太棒了! 暂时没有任务。</p></div>`;
                lucide.createIcons();
                return;
            }
            tasksToRender.sort((a,b) => a.deadline - b.deadline).forEach(task => {
                const isOverdue = daysUntil(task.deadline) <= 0;
                const deadlineText = isOverdue ? '已超期' : `剩余 ${daysUntil(task.deadline)} 天`;
                const priorityClasses = { high: 'border-rose-500', medium: 'border-yellow-500', low: 'border-sky-500' };
                const taskHTML = `<div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg border-l-4 ${priorityClasses[task.priority] || 'border-slate-600'}"><div class="flex items-center"><input type="checkbox" class="w-5 h-5 rounded-full bg-slate-700 border-slate-600 text-indigo-500 focus:ring-indigo-600"><div class="ml-4"><p class="text-white font-medium">${task.content}</p><p class="text-xs text-slate-400">${task.projectName || task.areaName}</p></div></div><div class="text-right"><span class="text-sm font-semibold ${isOverdue ? 'text-rose-400' : 'text-slate-300'}">${deadlineText}</span></div></div>`;
                container.innerHTML += taskHTML;
            });
        };

        const renderProjects = () => {
            const container = document.getElementById('project-radar');
            container.innerHTML = '';
            mockData.projects.forEach(project => {
                const daysLeft = daysUntil(project.deadline);
                const statusClasses = { 'On Track': 'bg-green-500/20 text-green-300', 'At Risk': 'bg-yellow-500/20 text-yellow-300', 'Overdue': 'bg-rose-500/20 text-rose-300' };
                const progressColor = { 'On Track': 'bg-green-500', 'At Risk': 'bg-yellow-500', 'Overdue': 'bg-rose-500' };
                const projectHTML = `
                    <div class="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 flex flex-col justify-between">
                        <div>
                            <div class="flex justify-between items-start">
                                <div><p class="text-xs text-slate-400">${project.areaName}</p><h3 class="font-bold text-white">${project.name}</h3></div>
                                <span class="text-xs font-bold px-2 py-1 rounded-full ${statusClasses[project.status] || ''}">${project.status}</span>
                            </div>
                            <div class="mt-4">
                                <div class="flex justify-between text-xs text-slate-400 mb-1"><span>进度</span><span>${project.progress}%</span></div>
                                <div class="w-full bg-slate-700 rounded-full h-2"><div class="${progressColor[project.status] || 'bg-slate-500'}" style="width: ${project.progress}%"></div></div>
                            </div>
                        </div>
                        <div class="mt-4 flex justify-between items-center text-xs text-slate-400 border-t border-slate-700 pt-3">
                            <span><i data-lucide="check-circle-2" class="w-3 h-3 inline-block mr-1"></i> ${project.tasksCompleted}/${project.tasksTotal} 任务</span>
                            <button class="gemini-task-btn text-indigo-400 hover:text-indigo-300 font-semibold flex items-center" data-project-name="${project.name}">
                                <i data-lucide="sparkles" class="w-3 h-3 inline-block mr-1"></i> AI 建议
                            </button>
                        </div>
                    </div>
                `;
                container.innerHTML += projectHTML;
            });
        };

        const renderAreas = () => {
            const container = document.getElementById('areas-habits');
            container.innerHTML = '';
            mockData.areas.forEach(area => {
                let habitsHTML = area.habits.map(habit => {
                    let recordsHTML = habit.records.map(done => `<span class="w-3 h-3 rounded-full ${done ? 'bg-emerald-500' : 'bg-slate-600'}"></span>`).join('');
                    return `<div class="flex justify-between items-center text-sm"><span class="text-slate-300">${habit.name}</span><div class="flex space-x-1.5">${recordsHTML}</div></div>`;
                }).join('');
                let metricsHTML = area.metrics.map(metric => `<div class="text-center"><p class="text-lg font-bold text-white">${metric.value}</p><p class="text-xs text-slate-400">${metric.name}</p></div>`).join('');
                const areaHTML = `<div class="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4"><h3 class="font-bold text-white">${area.name}</h3><p class="text-xs text-slate-400 italic mt-1">"${area.standard}"</p><div class="my-4 space-y-2">${habitsHTML}</div><div class="flex justify-around items-center border-t border-slate-700 pt-3">${metricsHTML}</div></div>`;
                container.innerHTML += areaHTML;
            });
        };

        // --- MODAL & GEMINI LOGIC ---
        const modal = document.getElementById('gemini-modal');
        const modalContent = document.getElementById('gemini-modal-content');
        const modalTitle = document.getElementById('gemini-modal-title');
        const modalBody = document.getElementById('gemini-modal-body');
        const loadingDiv = document.getElementById('gemini-loading');
        const resultDiv = document.getElementById('gemini-result');
        const actionBtn = document.getElementById('gemini-modal-action-btn');

        const openModal = () => {
            modal.classList.remove('hidden');
            modalContent.classList.remove('modal-leave');
            modalContent.classList.add('modal-enter');
        };

        const closeModal = () => {
            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-leave');
            setTimeout(() => modal.classList.add('hidden'), 300);
        };
        
        document.getElementById('gemini-modal-close').addEventListener('click', closeModal);
        document.getElementById('gemini-modal-cancel-btn').addEventListener('click', closeModal);
        
        async function handleGeminiRequest(prompt, title, showActionBtn = false) {
            openModal();
            modalTitle.innerHTML = `<i data-lucide="sparkles" class="w-5 h-5 mr-2 text-indigo-400"></i> ${title}`;
            lucide.createIcons();
            loadingDiv.classList.remove('hidden');
            resultDiv.classList.add('hidden');
            actionBtn.classList.add('hidden');

            const response = await callGeminiAPI(prompt);
            
            loadingDiv.classList.add('hidden');
            resultDiv.innerHTML = response;
            resultDiv.classList.remove('hidden');
            if (showActionBtn) {
                actionBtn.classList.remove('hidden');
            }
        }

        // --- INITIALIZATION ---
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('current-date').textContent = formatDate(MOCK_CURRENT_DATE);
            renderTasks('today');
            renderProjects();
            renderAreas();
            lucide.createIcons();

            // Tab switching logic
            document.querySelectorAll('.tab-btn').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active', 'text-indigo-400', 'border-indigo-400'));
                    tab.classList.add('active', 'text-indigo-400', 'border-indigo-400');
                    renderTasks(tab.dataset.tab);
                });
            });

            // Event listener for AI Review Button
            document.getElementById('gemini-review-btn').addEventListener('click', () => {
                const prompt = `为我的周复盘生成一份智能总结。这是我上周的数据：[这里可以聚合mockData中的相关信息]`;
                handleGeminiRequest(prompt, 'AI 智能周复盘');
            });

            // Event listener for AI Task Suggestion Buttons (using event delegation)
            document.getElementById('project-radar').addEventListener('click', (e) => {
                const button = e.target.closest('.gemini-task-btn');
                if (button) {
                    const projectName = button.dataset.projectName;
                    const prompt = `为项目 "${projectName}" 进行任务分解。`;
                    handleGeminiRequest(prompt, `"${projectName}" 的任务建议`, true);
                }
            });
        });
    </script>
</body>
</html>
