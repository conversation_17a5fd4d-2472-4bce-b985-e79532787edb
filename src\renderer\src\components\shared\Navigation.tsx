import { NavLink, useLocation } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { routes, type RouteCategory } from '../../lib/router'
import { useLanguage } from '../../contexts/LanguageContext'

// Icon components (simplified SVG icons)
const Icons = {
  chevronLeft: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
    </svg>
  ),
  chevronRight: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  ),
  dashboard: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
      />
    </svg>
  ),
  inbox: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
      />
    </svg>
  ),

  project: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
      />
    </svg>
  ),
  area: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  ),
  resource: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
  ),
  archive: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
      />
    </svg>
  ),
  review: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      />
    </svg>
  ),
  settings: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  )
}

// 将在组件内部使用翻译函数动态获取

// 分类方案重构：聚焦 PARA 主流程 + 工作流 + 系统
const categoryColors: Record<RouteCategory, string> = {
  main: 'default',
  para: 'secondary',
  tools: 'outline'
}

export function Navigation() {
  const location = useLocation()
  const { t } = useLanguage()
  const [isCollapsed, setIsCollapsed] = useState(false)

  // 动态获取分类标签
  const getCategoryLabel = (category: RouteCategory): string => {
    return t(`categories.${category}`)
  }

  // Group routes by category
  const routesByCategory = routes.reduce(
    (acc, route) => {
      if (!acc[route.category]) {
        acc[route.category] = [] as Array<(typeof routes)[number]>
      }
      acc[route.category].push(route)
      return acc
    },
    {} as Record<RouteCategory, Array<(typeof routes)[number]>>
  )

  // PARA 优先的展示顺序
  const orderedCategories: RouteCategory[] = ['para', 'main', 'tools']
  const entries = orderedCategories
    .filter((c) => routesByCategory[c] && routesByCategory[c].length)
    .map((c) => [c, routesByCategory[c]] as const)

  // 移除侧边栏搜索交互逻辑

  return (
    <>
      <nav className={cn(
        "flex flex-col h-full bg-sidebar text-sidebar-foreground border-r border-sidebar-border transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        {/* Logo/Brand 与收缩按钮 */}
        <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
          {!isCollapsed && <h1 className="text-lg font-bold">PaoLife</h1>}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 h-8 w-8"
          >
            {isCollapsed ? <Icons.chevronRight /> : <Icons.chevronLeft />}
          </Button>
        </div>

        {/* Navigation Links - 单一部分，按指定顺序 */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-1">
            {routes.map((route) => {
              const isActive = location.pathname === route.path || location.pathname.startsWith(route.path + '/')
              const IconComponent = Icons[route.icon as keyof typeof Icons]

              return (
                <NavLink
                  key={route.path}
                  to={route.path}
                  className={({ isActive: navIsActive }) =>
                    cn(
                      'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                      'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                      'hover:bg-muted/50 hover:text-foreground hover:shadow-sm',
                      navIsActive || isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'text-sidebar-foreground'
                    )
                  }
                >
                  <div className="flex items-center gap-3">
                    {IconComponent && (
                      <span className="flex-shrink-0">
                        <IconComponent />
                      </span>
                    )}
                    {!isCollapsed && (
                      <span className="font-medium">{t(`nav.${route.path.slice(1)}`)}</span>
                    )}
                  </div>
                </NavLink>
              )
            })}
          </div>
        </div>

        {/* 页脚 */}
        {!isCollapsed && (
          <div className="p-4 border-t border-sidebar-border">
            <div className="text-xs text-muted-foreground text-center">
              <p>泡生活 v1.0.0</p>
              <p>用 ❤️ 构建</p>
            </div>
          </div>
        )}
      </nav>

      {/* 全局搜索已移除 */}
    </>
  )
}

export default Navigation
